#!/bin/bash

# Quotese.com 开发服务器启动脚本
# 支持客户端路由的静态文件服务器

echo "🚀 Starting Quotese.com Development Server..."
echo ""

# 检查当前目录
if [ ! -f "index.html" ]; then
    echo "❌ Error: Please run this script from the frontend directory"
    echo "💡 Usage: cd frontend && ./start-dev-server.sh"
    exit 1
fi

# 默认端口
PORT=${PORT:-3001}

# 检查端口是否被占用
if lsof -Pi :$PORT -sTCP:LISTEN -t >/dev/null 2>&1; then
    echo "⚠️  Port $PORT is already in use"
    echo "🔍 Finding available port..."
    
    # 查找可用端口
    for port in {3002..3010}; do
        if ! lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
            PORT=$port
            echo "✅ Using port $PORT instead"
            break
        fi
    done
fi

# 选择服务器类型
echo "📋 Available server options:"
echo "  1) Node.js server (recommended)"
echo "  2) Python server"
echo "  3) Simple Python HTTP server (no routing support)"
echo ""

read -p "Choose server type (1-3) [1]: " choice
choice=${choice:-1}

case $choice in
    1)
        echo "🟢 Starting Node.js development server..."
        if command -v node >/dev/null 2>&1; then
            PORT=$PORT node dev-server.js
        else
            echo "❌ Node.js not found. Please install Node.js or choose option 2"
            exit 1
        fi
        ;;
    2)
        echo "🐍 Starting Python development server..."
        PORT=$PORT python3 dev_server.py
        ;;
    3)
        echo "⚠️  Starting simple Python HTTP server (no client routing support)..."
        echo "💡 Note: URLs like /quotes/1/ will return 404. Use option 1 or 2 for full functionality."
        python3 -m http.server $PORT
        ;;
    *)
        echo "❌ Invalid choice. Please run the script again."
        exit 1
        ;;
esac
