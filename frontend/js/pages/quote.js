/**
 * 名言详情页面控制器
 * 负责名言详情页面的数据加载和交互
 */

// 页面状态 - 使用命名空间避免与其他页面冲突
const quotePageState = {
    quoteId: null,
    authorId: null,
    isLoading: false,
    preloadedQuotes: new Map(), // 预加载的名言缓存
    lastPreloadTime: 0 // 上次预加载时间
};

/**
 * 初始化名言详情页面
 * @param {Object} params - 页面参数（可选，由PageRouter传递）
 */
async function initQuotePage(params = null) {
    try {
        // 如果有参数传入，使用传入的参数；否则从URL解析
        let quoteId;

        if (params && params.quoteId) {
            // 使用PageRouter传递的参数
            quoteId = params.quoteId;
            console.log('Quote page: Using quoteId from PageRouter:', quoteId);
        } else {
            // 向后兼容：从URL获取名言ID
            quoteId = UrlHandler.parseQuoteIdFromPath();
            if (!quoteId) {
                showErrorMessage('Quote not found. Please check the URL.');
                return;
            }
            console.log('Quote page: Parsed quoteId from URL:', quoteId);
        }

        quotePageState.quoteId = quoteId;

        // 加载组件
        await loadPageComponents();

        // 加载数据
        await loadPageData();

        // 初始化事件监听器
        initEventListeners();

    } catch (error) {
        console.error('Error initializing quote page:', error);
        showErrorMessage('Failed to initialize page. Please try refreshing.');
    }
}

/**
 * 加载页面组件
 */
async function loadPageComponents() {
    // 加载面包屑组件
    await ComponentLoader.loadComponent('breadcrumb-container', 'breadcrumb');

    // 初始化面包屑导航
    BreadcrumbComponent.init('breadcrumb-list');

    // 加载热门主题组件
    await ComponentLoader.loadComponent('popular-topics-container', 'popular-topics');
}

/**
 * 加载页面数据
 * @param {number} retryCount - 重试次数
 */
async function loadPageData(retryCount = 0) {
    const maxRetries = 3;

    // 显示加载状态
    showLoadingState();

    try {
        // 获取名言详情
        console.log('Getting quote by ID:', quotePageState.quoteId, `(attempt ${retryCount + 1})`);
        const quote = await window.ApiClient.getQuote(quotePageState.quoteId);
        console.log('Quote result:', quote);
        if (!quote) {
            // 显示404页面，因为名言不存在
            showErrorMessage(`Quote with ID ${quotePageState.quoteId} not found.`, false, true);
            hideLoadingState();
            return;
        }

        // 更新页面标题和描述
        updatePageMetadata(quote);

        // 渲染名言详情
        renderQuoteDetails(quote);

        // 保存作者ID
        quotePageState.authorId = quote.author.id;

        // 并行加载其他数据
        const [categoriesData, authorsData, sourcesData, relatedQuotesData] = await Promise.all([
            loadCategories(),
            loadAuthors(),
            loadSources(),
            loadRelatedQuotes()
        ]);

        // 隐藏加载状态
        hideLoadingState();

        // 启动预加载相邻名言
        preloadAdjacentQuotes();

    } catch (error) {
        console.error('Error loading page data:', error, `(attempt ${retryCount + 1})`);

        // 如果还有重试次数，自动重试
        if (retryCount < maxRetries) {
            console.log(`Retrying in 2 seconds... (${retryCount + 1}/${maxRetries})`);
            setTimeout(() => {
                loadPageData(retryCount + 1);
            }, 2000);

            // 显示重试状态
            const quoteCardContainer = document.getElementById('quote-card-container');
            if (quoteCardContainer) {
                quoteCardContainer.innerHTML = `
                    <div class="text-center py-16">
                        <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-500 mb-4"></div>
                        <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">Connection Error</h3>
                        <p class="text-gray-600 dark:text-gray-400">Retrying... (${retryCount + 1}/${maxRetries})</p>
                    </div>
                `;
            }
        } else {
            // 所有重试都失败了，显示错误消息
            showErrorMessage(`Failed to load quote data after ${maxRetries} attempts. Please check your connection and try again.`, true, false);
            hideLoadingState();
        }
    }
}

/**
 * 更新页面元数据
 * @param {Object} quote - 名言对象
 */
function updatePageMetadata(quote) {
    // 截断长名言
    const shortQuote = quote.content.length > 100
        ? quote.content.substring(0, 100) + '...'
        : quote.content;

    document.title = `"${shortQuote}" - ${quote.author.name} | Quotes Collection`;

    // 更新meta描述
    const metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription) {
        metaDescription.content = `"${shortQuote}" - ${quote.author.name}. Explore this inspiring quote and more wisdom from ${quote.author.name}.`;
    }

    // 更新canonical链接
    const canonicalLink = document.querySelector('link[rel="canonical"]');
    if (canonicalLink) {
        canonicalLink.href = `https://quotese.com/quotes/${UrlHandler.slugify(quote.content.substring(0, 50))}-${quote.id}.html`;
    }

    // 添加Quote结构化数据
    addQuoteStructuredData(quote);

    // 更新社交媒体元数据
    if (window.SocialMetaUtil) {
        window.SocialMetaUtil.updateQuoteMetaTags(quote);
    }
}

/**
 * 渲染名言详情
 * @param {Object} quote - 名言对象
 */
function renderQuoteDetails(quote) {
    // 更新名言内容
    const contentElement = document.getElementById('quote-content');
    if (contentElement) {
        contentElement.textContent = `"${quote.content}"`;
    }

    // 更新作者信息
    const authorLinkElement = document.getElementById('author-link');
    if (authorLinkElement) {
        authorLinkElement.href = UrlHandler.getAuthorUrl(quote.author);
        authorLinkElement.textContent = quote.author.name;
    }

    // 更新作者首字母
    const authorInitialElement = document.getElementById('author-initial');
    if (authorInitialElement) {
        authorInitialElement.textContent = quote.author.name.charAt(0);
    }

    // 更新来源信息
    const sourceTextElement = document.getElementById('source-text');
    if (sourceTextElement) {
        sourceTextElement.innerHTML = quote.sources.map(source =>
            `<a href="${UrlHandler.getSourceUrl(source)}" class="hover:text-yellow-600 dark:hover:text-yellow-400 transition-colors duration-300">${source.name}</a>`
        ).join(', ');
    }

    // 更新类别标签
    const categoriesContainer = document.getElementById('categories-container');
    if (categoriesContainer) {
        categoriesContainer.innerHTML = '';

        quote.categories.forEach(category => {
            const categoryTag = document.createElement('a');
            categoryTag.href = UrlHandler.getCategoryUrl(category);
            categoryTag.className = 'tag px-3 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200 hover:bg-yellow-200 dark:hover:bg-yellow-800 transition-colors duration-300';
            categoryTag.textContent = category.name;

            categoriesContainer.appendChild(categoryTag);
        });
    }

    // 更新日期信息
    const dateElement = document.getElementById('quote-date');
    if (dateElement) {
        const date = new Date(quote.updatedAt || quote.createdAt);
        dateElement.textContent = `Updated: ${date.toLocaleDateString()}`;
    }

    // 更新相关名言标题
    const authorHeadingElement = document.getElementById('author-name-heading');
    if (authorHeadingElement) {
        authorHeadingElement.textContent = quote.author.name;
    }
}

/**
 * 加载类别列表
 * @returns {Promise<Object>} - 类别列表
 */
async function loadCategories() {
    try {
        // 获取类别列表
        const categoriesData = await window.ApiClient.getCategories(20);

        // 渲染类别列表
        renderCategories(categoriesData.categories);

        return categoriesData;
    } catch (error) {
        console.error('Error loading categories:', error);
        throw error;
    }
}

/**
 * 加载作者列表
 * @returns {Promise<Object>} - 作者列表
 */
async function loadAuthors() {
    try {
        // 获取作者列表
        const authorsData = await window.ApiClient.getAuthors(5);

        // 渲染作者列表
        renderAuthors(authorsData.authors);

        return authorsData;
    } catch (error) {
        console.error('Error loading authors:', error);
        throw error;
    }
}

/**
 * 加载来源列表
 * @returns {Promise<Object>} - 来源列表
 */
async function loadSources() {
    try {
        // 获取来源列表
        const sourcesData = await window.ApiClient.getSources(5);

        // 渲染来源列表
        renderSources(sourcesData.sources);

        return sourcesData;
    } catch (error) {
        console.error('Error loading sources:', error);
        throw error;
    }
}

/**
 * 加载相关名言
 * @returns {Promise<Object>} - 相关名言列表
 */
async function loadRelatedQuotes() {
    try {
        // 获取相关名言列表（同一作者的其他名言）
        const filters = { authorId: quotePageState.authorId };
        const quotesData = await window.ApiClient.getQuotes(1, 5, filters);

        // 过滤掉当前名言
        const relatedQuotes = quotesData.quotes.filter(quote => quote.id !== parseInt(quotePageState.quoteId));

        // 渲染相关名言列表
        renderRelatedQuotes(relatedQuotes);

        return quotesData;
    } catch (error) {
        console.error('Error loading related quotes:', error);
        throw error;
    }
}

/**
 * 渲染相关名言列表
 * @param {Array} quotes - 名言列表
 */
function renderRelatedQuotes(quotes) {
    const relatedQuotesContainer = document.getElementById('related-quotes-container');
    if (!relatedQuotesContainer) return;

    // 清空容器
    relatedQuotesContainer.innerHTML = '';

    if (quotes.length === 0) {
        // 显示空状态
        relatedQuotesContainer.innerHTML = `
            <div class="text-center py-8">
                <p class="text-gray-500 dark:text-gray-400">No other quotes found by this author.</p>
            </div>
        `;
        return;
    }

    // 创建名言卡片
    quotes.forEach((quote, index) => {
        const quoteCard = document.createElement('div');
        quoteCard.className = `quote-card-component relative p-4 sm:p-5 md:p-6 quote-marks fade-in fade-in-delay-${index % 3} mb-4 sm:mb-6 card-hover-effect cursor-pointer`;

        // 截断长名言
        const displayContent = quote.content.length > 150
            ? quote.content.substring(0, 150) + '...'
            : quote.content;

        // 创建名言卡片内容
        quoteCard.innerHTML = `
            <div class="relative z-10">
                <p class="text-base sm:text-lg font-serif leading-relaxed mb-3 sm:mb-4">"${displayContent}"</p>
                <div class="flex items-center mt-3 sm:mt-4">
                    <div class="flex-shrink-0 mr-4">
                        <div class="w-10 h-10 rounded-full bg-yellow-100 dark:bg-yellow-900 flex items-center justify-center text-yellow-600 dark:text-yellow-300 border-2 border-yellow-400 dark:border-yellow-600">
                            <span class="text-sm font-bold">${quote.author.name.charAt(0)}</span>
                        </div>
                    </div>
                    <div>
                        <a href="${UrlHandler.getAuthorUrl(quote.author)}" class="font-semibold text-yellow-600 dark:text-yellow-400 hover:text-yellow-700 dark:hover:text-yellow-300 transition-colors duration-300">${quote.author.name}</a>
                        <p class="text-sm text-gray-500 dark:text-gray-400">${quote.sources.map(source => source.name).join(', ')}</p>
                    </div>
                </div>
            </div>
            <div class="mt-3 sm:mt-4 flex flex-wrap gap-1 sm:gap-2">
                ${quote.categories.map(category =>
                    `<a href="${UrlHandler.getCategoryUrl(category)}" class="tag px-2 py-0.5 sm:px-3 sm:py-1 text-xs rounded-full bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200 hover:bg-yellow-200 dark:hover:bg-yellow-800 transition-colors duration-300">${category.name}</a>`
                ).join('')}
            </div>
            <div class="absolute bottom-2 sm:bottom-3 right-2 sm:right-4 flex items-center space-x-1">
                <button class="p-2 text-gray-500 hover:text-yellow-600 dark:text-gray-400 dark:hover:text-yellow-400 transition-colors duration-300 rounded-full hover:bg-yellow-50 dark:hover:bg-gray-700" title="Share" data-quote-id="${quote.id}">
                    <i class="fas fa-share-alt"></i>
                </button>
                <button class="p-2 text-gray-500 hover:text-yellow-600 dark:text-gray-400 dark:hover:text-yellow-400 transition-colors duration-300 rounded-full hover:bg-yellow-50 dark:hover:bg-gray-700" title="View Details" data-quote-id="${quote.id}">
                    <i class="fas fa-external-link-alt"></i>
                </button>
            </div>
        `;

        // 添加到容器
        relatedQuotesContainer.appendChild(quoteCard);

        // 添加点击事件
        quoteCard.addEventListener('click', (e) => {
            // 只有在没有点击按钮或链接时才触发
            if (!e.target.closest('button') && !e.target.closest('a')) {
                window.location.href = UrlHandler.getQuoteUrl({
                    id: quote.id,
                    content: quote.content
                });
            }
        });
    });
}

/**
 * 渲染类别列表
 * @param {Array} categories - 类别列表
 */
function renderCategories(categories) {
    const categoriesContainer = document.getElementById('categories-container');
    if (!categoriesContainer) return;

    // 清空容器
    categoriesContainer.innerHTML = '';

    if (categories.length === 0) {
        categoriesContainer.innerHTML = '<p class="text-gray-500 dark:text-gray-400">No categories found.</p>';
        return;
    }

    // 按照引用数量排序
    const sortedCategories = [...categories].sort((a, b) => b.count - a.count);

    // 创建类别标签
    sortedCategories.forEach(category => {
        const categoryTag = document.createElement('a');
        categoryTag.href = UrlHandler.getCategoryUrl(category);
        categoryTag.className = 'tag px-3 py-1 text-sm rounded-full bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200 hover:bg-yellow-200 dark:hover:bg-yellow-800 transition-colors duration-300';
        categoryTag.textContent = `${category.name} (${category.count})`;

        categoriesContainer.appendChild(categoryTag);
    });
}

/**
 * 渲染作者列表
 * @param {Array} authors - 作者列表
 */
function renderAuthors(authors) {
    const authorsContainer = document.getElementById('authors-container');
    if (!authorsContainer) return;

    // 清空容器
    authorsContainer.innerHTML = '';

    if (authors.length === 0) {
        authorsContainer.innerHTML = '<p class="text-gray-500 dark:text-gray-400">No authors found.</p>';
        return;
    }

    // 按照引用数量排序
    const sortedAuthors = [...authors].sort((a, b) => b.count - a.count);

    // 创建作者列表项
    sortedAuthors.forEach(author => {
        const maxCount = sortedAuthors[0].count;
        const percentage = Math.round((author.count / maxCount) * 100);

        const authorItem = document.createElement('li');

        // 当前作者高亮显示
        if (author.id === quotePageState.authorId) {
            authorItem.className = 'flex justify-between items-center p-2 rounded-md bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800';
        } else {
            authorItem.className = 'flex justify-between items-center p-2 rounded-md hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors duration-300';
        }

        authorItem.innerHTML = `
            <div class="flex items-center space-x-2 w-full">
                <div class="w-8 h-8 rounded-full bg-yellow-100 dark:bg-yellow-900 flex items-center justify-center text-yellow-600 dark:text-yellow-300 flex-shrink-0 border-2 border-yellow-400 dark:border-yellow-600">
                    <span class="text-xs font-bold">${author.name.charAt(0)}</span>
                </div>
                <div class="flex-grow ml-2">
                    <a href="${UrlHandler.getAuthorUrl(author)}" class="${author.id === quotePageState.authorId ? 'text-yellow-600 dark:text-yellow-400 font-semibold' : 'hover:text-yellow-600 dark:hover:text-yellow-400'} font-medium transition-colors duration-300">${author.name}</a>
                    <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5 mt-1">
                        <div class="bg-yellow-500 dark:bg-yellow-400 h-1.5 rounded-full" style="width: ${percentage}%"></div>
                    </div>
                </div>
                <span class="text-sm text-gray-500 dark:text-gray-400 ml-2">${author.count}</span>
            </div>
        `;

        authorsContainer.appendChild(authorItem);
    });
}

/**
 * 渲染来源列表
 * @param {Array} sources - 来源列表
 */
function renderSources(sources) {
    const sourcesContainer = document.getElementById('sources-container');
    if (!sourcesContainer) return;

    // 清空容器
    sourcesContainer.innerHTML = '';

    if (sources.length === 0) {
        sourcesContainer.innerHTML = '<p class="text-gray-500 dark:text-gray-400">No sources found.</p>';
        return;
    }

    // 按照引用数量排序
    const sortedSources = [...sources].sort((a, b) => b.count - a.count);

    // 创建来源列表项
    sortedSources.forEach(source => {
        const sourceItem = document.createElement('li');
        sourceItem.className = 'flex justify-between items-center p-2 rounded-md hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors duration-300';

        // 根据来源类型选择不同的图标
        let iconClass = 'fas fa-book';

        if (source.name.toLowerCase().includes('speech') || source.name.toLowerCase().includes('address')) {
            iconClass = 'fas fa-microphone';
        } else if (source.name.toLowerCase().includes('interview')) {
            iconClass = 'fas fa-comments';
        } else if (source.name.toLowerCase().includes('letter') || source.name.toLowerCase().includes('correspondence')) {
            iconClass = 'fas fa-envelope';
        } else if (source.name.toLowerCase().includes('article') || source.name.toLowerCase().includes('magazine')) {
            iconClass = 'fas fa-newspaper';
        } else if (source.name.toLowerCase().includes('movie') || source.name.toLowerCase().includes('film')) {
            iconClass = 'fas fa-film';
        } else if (source.name.toLowerCase().includes('song') || source.name.toLowerCase().includes('music')) {
            iconClass = 'fas fa-music';
        }

        sourceItem.innerHTML = `
            <div class="flex items-center space-x-2 w-full">
                <div class="w-8 h-8 rounded-full bg-yellow-100 dark:bg-yellow-900 flex items-center justify-center text-yellow-600 dark:text-yellow-300 flex-shrink-0">
                    <i class="${iconClass} text-xs"></i>
                </div>
                <div class="flex-grow">
                    <a href="${UrlHandler.getSourceUrl(source)}" class="hover:text-yellow-600 dark:hover:text-yellow-400 font-medium transition-colors duration-300">${source.name}</a>
                </div>
                <span class="text-sm text-gray-500 dark:text-gray-400 ml-2">${source.count}</span>
            </div>
        `;

        sourcesContainer.appendChild(sourceItem);
    });
}

/**
 * 显示加载状态
 */
function showLoadingState() {
    quotePageState.isLoading = true;

    // 显示名言详情加载状态
    const quoteCardContainer = document.getElementById('quote-card-container');
    if (quoteCardContainer) {
        quoteCardContainer.innerHTML = `
            <div class="text-center py-16">
                <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-500 mb-4"></div>
                <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">Loading Quote</h3>
                <p class="text-gray-600 dark:text-gray-400">Please wait while we fetch the quote details...</p>
            </div>
        `;
    }

    // 显示相关名言加载状态
    const relatedQuotesContainer = document.getElementById('related-quotes-container');
    if (relatedQuotesContainer) {
        relatedQuotesContainer.innerHTML = `
            <div class="text-center py-12">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-500 mb-3"></div>
                <p class="text-gray-600 dark:text-gray-400">Loading related quotes...</p>
            </div>
        `;
    }

    // 显示侧边栏加载状态
    const popularTopicsContainer = document.getElementById('popular-topics-container');
    if (popularTopicsContainer) {
        popularTopicsContainer.innerHTML = `
            <div class="text-center py-8">
                <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-yellow-500 mb-2"></div>
                <p class="text-sm text-gray-600 dark:text-gray-400">Loading topics...</p>
            </div>
        `;
    }
}

/**
 * 隐藏加载状态
 */
function hideLoadingState() {
    quotePageState.isLoading = false;
}

/**
 * 显示错误消息
 * @param {string} message - 错误消息
 * @param {boolean} showRetry - 是否显示重试按钮
 * @param {boolean} show404 - 是否显示404页面
 */
function showErrorMessage(message, showRetry = true, show404 = false) {
    const quoteCardContainer = document.getElementById('quote-card-container');
    if (quoteCardContainer) {
        if (show404) {
            // 显示404页面
            quoteCardContainer.innerHTML = `
                <div class="text-center py-16">
                    <div class="max-w-md mx-auto">
                        <div class="text-6xl font-bold text-yellow-500 mb-4">404</div>
                        <h1 class="text-2xl font-bold text-gray-800 dark:text-gray-200 mb-4">Quote Not Found</h1>
                        <p class="text-gray-600 dark:text-gray-400 mb-8">
                            The quote you're looking for doesn't exist or has been removed.
                        </p>
                        <div class="space-y-4">
                            <a href="/" class="inline-block bg-yellow-500 text-white px-6 py-3 rounded-lg hover:bg-yellow-600 transition-colors duration-300">
                                <i class="fas fa-home mr-2"></i>
                                Go Home
                            </a>
                            <div class="text-sm text-gray-500 dark:text-gray-400">
                                <p>You can also try:</p>
                                <div class="mt-2 space-x-4">
                                    <a href="/quotes/" class="text-yellow-500 hover:text-yellow-600">Browse All Quotes</a>
                                    <a href="/authors/" class="text-yellow-500 hover:text-yellow-600">Browse Authors</a>
                                    <a href="/categories/" class="text-yellow-500 hover:text-yellow-600">Browse Categories</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        } else {
            // 显示一般错误消息
            quoteCardContainer.innerHTML = `
                <div class="bg-red-100 dark:bg-red-900/20 border border-red-400 dark:border-red-800 text-red-800 dark:text-red-200 p-6 rounded-lg">
                    <div class="flex items-center mb-4">
                        <i class="fas fa-exclamation-circle mr-3 text-xl"></i>
                        <h3 class="text-lg font-semibold">Error Loading Quote</h3>
                    </div>
                    <p class="mb-4">${message}</p>
                    ${showRetry ? `
                        <div class="flex space-x-4">
                            <button id="retry-button" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md transition-colors duration-300">
                                <i class="fas fa-redo mr-2"></i>
                                Retry
                            </button>
                            <a href="/" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md transition-colors duration-300">
                                <i class="fas fa-home mr-2"></i>
                                Go Home
                            </a>
                        </div>
                    ` : ''}
                </div>
            `;

            // 添加重试按钮事件监听器
            if (showRetry) {
                const retryButton = document.getElementById('retry-button');
                if (retryButton) {
                    retryButton.addEventListener('click', () => {
                        console.log('Retrying quote load...');
                        loadPageData();
                    });
                }
            }
        }
    }
}

/**
 * 初始化事件监听器
 */
function initEventListeners() {
    // 分享按钮
    const shareButton = document.getElementById('share-button');
    if (shareButton) {
        shareButton.addEventListener('click', shareQuote);
    }

    // 复制按钮
    const copyButton = document.getElementById('copy-button');
    if (copyButton) {
        copyButton.addEventListener('click', copyQuoteToClipboard);
    }
}

/**
 * 分享名言
 */
function shareQuote() {
    // 如果支持Web Share API
    if (navigator.share) {
        const quoteContent = document.getElementById('quote-content').textContent;
        const authorName = document.getElementById('author-link').textContent;

        navigator.share({
            title: `Quote by ${authorName}`,
            text: `${quoteContent} - ${authorName}`,
            url: window.location.href
        })
        .then(() => console.log('Quote shared successfully'))
        .catch((error) => console.error('Error sharing quote:', error));
    } else {
        // 如果不支持Web Share API，复制链接到剪贴板
        copyToClipboard(window.location.href);

        // 显示提示
        alert('Link copied to clipboard!');
    }
}

/**
 * 复制名言到剪贴板
 */
function copyQuoteToClipboard() {
    const quoteContent = document.getElementById('quote-content').textContent;
    const authorName = document.getElementById('author-link').textContent;

    const textToCopy = `${quoteContent} - ${authorName}`;

    copyToClipboard(textToCopy);

    // 显示提示
    alert('Quote copied to clipboard!');
}

/**
 * 复制文本到剪贴板
 * @param {string} text - 要复制的文本
 */
function copyToClipboard(text) {
    // 创建临时元素
    const tempElement = document.createElement('textarea');
    tempElement.value = text;
    tempElement.setAttribute('readonly', '');
    tempElement.style.position = 'absolute';
    tempElement.style.left = '-9999px';

    document.body.appendChild(tempElement);

    // 选择文本并复制
    tempElement.select();
    document.execCommand('copy');

    // 移除临时元素
    document.body.removeChild(tempElement);
}

/**
 * 添加Quote结构化数据
 * @param {Object} quote - 名言对象
 */
function addQuoteStructuredData(quote) {
    // 移除旧的结构化数据
    const oldScript = document.getElementById('quote-structured-data');
    if (oldScript) {
        oldScript.remove();
    }

    // 创建结构化数据
    const structuredData = {
        "@context": "https://schema.org",
        "@type": "Quotation",
        "text": quote.content,
        "author": {
            "@type": "Person",
            "name": quote.author.name,
            "url": `https://quotese.com${UrlHandler.getAuthorUrl(quote.author)}`
        },
        "dateCreated": quote.createdAt || new Date().toISOString(),
        "mainEntityOfPage": {
            "@type": "WebPage",
            "@id": `https://quotese.com${UrlHandler.getQuoteUrl(quote)}`
        }
    };

    // 添加来源信息（如果有）
    if (quote.sources && quote.sources.length > 0) {
        structuredData.isPartOf = {
            "@type": "CreativeWork",
            "name": quote.sources[0].name,
            "url": `https://quotese.com${UrlHandler.getSourceUrl(quote.sources[0])}`
        };
    }

    // 添加类别信息（如果有）
    if (quote.categories && quote.categories.length > 0) {
        structuredData.keywords = quote.categories.map(category => category.name).join(',');
    }

    // 添加到页面
    const script = document.createElement('script');
    script.id = 'quote-structured-data';
    script.type = 'application/ld+json';
    script.textContent = JSON.stringify(structuredData);
    document.head.appendChild(script);
}

/**
 * 预加载相邻名言
 * 预加载当前名言ID前后的名言，提升用户体验
 */
async function preloadAdjacentQuotes() {
    const currentId = parseInt(quotePageState.quoteId);
    if (isNaN(currentId)) return;

    // 避免频繁预加载
    const now = Date.now();
    if (now - quotePageState.lastPreloadTime < 30000) { // 30秒内不重复预加载
        return;
    }
    quotePageState.lastPreloadTime = now;

    // 预加载前后各2个名言
    const preloadIds = [
        currentId - 2,
        currentId - 1,
        currentId + 1,
        currentId + 2
    ].filter(id => id > 0 && !quotePageState.preloadedQuotes.has(id));

    console.log('Preloading adjacent quotes:', preloadIds);

    // 并行预加载，但不阻塞主线程
    preloadIds.forEach(async (id) => {
        try {
            const quote = await window.ApiClient.getQuote(id, true); // 使用缓存
            if (quote) {
                quotePageState.preloadedQuotes.set(id, quote);
                console.log(`Preloaded quote ${id}`);
            }
        } catch (error) {
            console.warn(`Failed to preload quote ${id}:`, error);
        }
    });

    // 清理过期的预加载缓存（保留最近20个）
    if (quotePageState.preloadedQuotes.size > 20) {
        const entries = Array.from(quotePageState.preloadedQuotes.entries());
        const toDelete = entries.slice(0, entries.length - 20);
        toDelete.forEach(([id]) => {
            quotePageState.preloadedQuotes.delete(id);
        });
    }
}

/**
 * 优化图片懒加载
 * 为页面中的图片添加懒加载功能
 */
function initImageLazyLoading() {
    // 检查浏览器是否支持Intersection Observer
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    if (img.dataset.src) {
                        img.src = img.dataset.src;
                        img.removeAttribute('data-src');
                        observer.unobserve(img);
                    }
                }
            });
        }, {
            rootMargin: '50px 0px', // 提前50px开始加载
            threshold: 0.1
        });

        // 观察所有带有data-src属性的图片
        document.querySelectorAll('img[data-src]').forEach(img => {
            imageObserver.observe(img);
        });
    } else {
        // 降级处理：直接加载所有图片
        document.querySelectorAll('img[data-src]').forEach(img => {
            img.src = img.dataset.src;
            img.removeAttribute('data-src');
        });
    }
}

/**
 * 优化缓存策略
 * 实现智能缓存清理和预热
 */
function optimizeCacheStrategy() {
    // 检查API客户端缓存大小
    if (window.ApiClient && window.ApiClient.cache) {
        const cacheSize = Object.keys(window.ApiClient.cache).length;
        console.log(`API cache size: ${cacheSize} entries`);

        // 如果缓存过大，清理最旧的条目
        if (cacheSize > 100) {
            const cacheEntries = Object.entries(window.ApiClient.cache);
            const toDelete = cacheEntries.slice(0, cacheEntries.length - 50);
            toDelete.forEach(([key]) => {
                delete window.ApiClient.cache[key];
            });
            console.log(`Cleaned ${toDelete.length} cache entries`);
        }
    }

    // 预热常用数据
    preloadCommonData();
}

/**
 * 预加载常用数据
 * 预加载热门类别、作者等常用数据
 */
async function preloadCommonData() {
    try {
        // 预加载热门类别（小数据量）
        window.ApiClient.getPopularCategories(10, true);

        // 预加载热门作者（小数据量）
        window.ApiClient.getPopularAuthors(10, true);

        console.log('Common data preloaded');
    } catch (error) {
        console.warn('Failed to preload common data:', error);
    }
}

/**
 * 性能监控
 * 监控页面性能指标
 */
function initPerformanceMonitoring() {
    // 监控页面加载时间
    if ('performance' in window) {
        window.addEventListener('load', () => {
            setTimeout(() => {
                const perfData = performance.getEntriesByType('navigation')[0];
                if (perfData) {
                    console.log('Page Performance:', {
                        loadTime: Math.round(perfData.loadEventEnd - perfData.loadEventStart),
                        domContentLoaded: Math.round(perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart),
                        totalTime: Math.round(perfData.loadEventEnd - perfData.fetchStart)
                    });
                }
            }, 0);
        });
    }

    // 监控API调用性能
    if (window.ApiClient) {
        const originalQuery = window.ApiClient.query;
        window.ApiClient.query = async function(...args) {
            const startTime = performance.now();
            try {
                const result = await originalQuery.apply(this, args);
                const endTime = performance.now();
                console.log(`API Query took ${Math.round(endTime - startTime)}ms`);
                return result;
            } catch (error) {
                const endTime = performance.now();
                console.warn(`API Query failed after ${Math.round(endTime - startTime)}ms:`, error);
                throw error;
            }
        };
    }
}

/**
 * 预加载相邻名言
 * 预加载当前名言ID前后的名言，提升用户体验
 */
async function preloadAdjacentQuotes() {
    const currentId = parseInt(quotePageState.quoteId);
    if (isNaN(currentId)) return;

    // 避免频繁预加载
    const now = Date.now();
    if (now - quotePageState.lastPreloadTime < 30000) { // 30秒内不重复预加载
        return;
    }
    quotePageState.lastPreloadTime = now;

    // 预加载前后各2个名言
    const preloadIds = [
        currentId - 2,
        currentId - 1,
        currentId + 1,
        currentId + 2
    ].filter(id => id > 0 && !quotePageState.preloadedQuotes.has(id));

    console.log('Preloading adjacent quotes:', preloadIds);

    // 并行预加载，但不阻塞主线程
    preloadIds.forEach(async (id) => {
        try {
            const quote = await window.ApiClient.getQuote(id, true); // 使用缓存
            if (quote) {
                quotePageState.preloadedQuotes.set(id, quote);
                console.log(`Preloaded quote ${id}`);
            }
        } catch (error) {
            console.warn(`Failed to preload quote ${id}:`, error);
        }
    });

    // 清理过期的预加载缓存（保留最近20个）
    if (quotePageState.preloadedQuotes.size > 20) {
        const entries = Array.from(quotePageState.preloadedQuotes.entries());
        const toDelete = entries.slice(0, entries.length - 20);
        toDelete.forEach(([id]) => {
            quotePageState.preloadedQuotes.delete(id);
        });
    }
}

/**
 * 优化图片懒加载
 * 为页面中的图片添加懒加载功能
 */
function initImageLazyLoading() {
    // 检查浏览器是否支持Intersection Observer
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    if (img.dataset.src) {
                        img.src = img.dataset.src;
                        img.removeAttribute('data-src');
                        observer.unobserve(img);
                    }
                }
            });
        }, {
            rootMargin: '50px 0px', // 提前50px开始加载
            threshold: 0.1
        });

        // 观察所有带有data-src属性的图片
        document.querySelectorAll('img[data-src]').forEach(img => {
            imageObserver.observe(img);
        });
    } else {
        // 降级处理：直接加载所有图片
        document.querySelectorAll('img[data-src]').forEach(img => {
            img.src = img.dataset.src;
            img.removeAttribute('data-src');
        });
    }
}

/**
 * 优化缓存策略
 * 实现智能缓存清理和预热
 */
function optimizeCacheStrategy() {
    // 检查API客户端缓存大小
    if (window.ApiClient && window.ApiClient.cache) {
        const cacheSize = Object.keys(window.ApiClient.cache).length;
        console.log(`API cache size: ${cacheSize} entries`);

        // 如果缓存过大，清理最旧的条目
        if (cacheSize > 100) {
            const cacheEntries = Object.entries(window.ApiClient.cache);
            const toDelete = cacheEntries.slice(0, cacheEntries.length - 50);
            toDelete.forEach(([key]) => {
                delete window.ApiClient.cache[key];
            });
            console.log(`Cleaned ${toDelete.length} cache entries`);
        }
    }

    // 预热常用数据
    preloadCommonData();
}

/**
 * 预加载常用数据
 * 预加载热门类别、作者等常用数据
 */
async function preloadCommonData() {
    try {
        // 预加载热门类别（小数据量）
        window.ApiClient.getPopularCategories(10, true);

        // 预加载热门作者（小数据量）
        window.ApiClient.getPopularAuthors(10, true);

        console.log('Common data preloaded');
    } catch (error) {
        console.warn('Failed to preload common data:', error);
    }
}

/**
 * 性能监控
 * 监控页面性能指标
 */
function initPerformanceMonitoring() {
    // 监控页面加载时间
    if ('performance' in window) {
        window.addEventListener('load', () => {
            setTimeout(() => {
                const perfData = performance.getEntriesByType('navigation')[0];
                if (perfData) {
                    console.log('Page Performance:', {
                        loadTime: Math.round(perfData.loadEventEnd - perfData.loadEventStart),
                        domContentLoaded: Math.round(perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart),
                        totalTime: Math.round(perfData.loadEventEnd - perfData.fetchStart)
                    });
                }
            }, 0);
        });
    }

    // 监控API调用性能
    if (window.ApiClient) {
        const originalQuery = window.ApiClient.query;
        window.ApiClient.query = async function(...args) {
            const startTime = performance.now();
            try {
                const result = await originalQuery.apply(this, args);
                const endTime = performance.now();
                console.log(`API Query took ${Math.round(endTime - startTime)}ms`);
                return result;
            } catch (error) {
                const endTime = performance.now();
                console.warn(`API Query failed after ${Math.round(endTime - startTime)}ms:`, error);
                throw error;
            }
        };
    }
}

// 页面初始化现在由PageRouter负责
// 保留此函数供PageRouter调用

// 初始化性能优化功能
document.addEventListener('DOMContentLoaded', () => {
    // 延迟初始化性能优化功能，避免阻塞主要内容加载
    setTimeout(() => {
        initImageLazyLoading();
        optimizeCacheStrategy();
        initPerformanceMonitoring();
    }, 1000);
});

// 初始化性能优化功能
document.addEventListener('DOMContentLoaded', () => {
    // 延迟初始化性能优化功能，避免阻塞主要内容加载
    setTimeout(() => {
        initImageLazyLoading();
        optimizeCacheStrategy();
        initPerformanceMonitoring();
    }, 1000);
});
