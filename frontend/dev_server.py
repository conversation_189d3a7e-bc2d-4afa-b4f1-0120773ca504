#!/usr/bin/env python3
"""
开发服务器 - 支持客户端路由的静态文件服务器
解决SPA应用的路由重写问题
"""

import os
import sys
import mimetypes
import urllib.parse
from http.server import HTTPServer, BaseHTTPRequestHandler
from datetime import datetime

# 服务器配置
PORT = int(os.environ.get('PORT', 3001))
HOST = os.environ.get('HOST', 'localhost')
ROOT_DIR = os.path.dirname(os.path.abspath(__file__))  # frontend目录

# 客户端路由规则 - 这些路径应该返回相应的HTML页面
CLIENT_ROUTES = {
    # 名言详情页路由
    '/quotes/': 'quote.html',
    # 分类页面路由
    '/categories/': 'category.html',
    # 作者页面路由
    '/authors/': 'author.html',
    # 来源页面路由
    '/sources/': 'source.html',
    # 默认首页
    '/': 'index.html'
}

class DevServerHandler(BaseHTTPRequestHandler):
    """支持客户端路由的HTTP请求处理器"""
    
    def log_message(self, format, *args):
        """自定义日志格式"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f"{timestamp} - {format % args}")
    
    def end_headers(self):
        """添加CORS头"""
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        super().end_headers()
    
    def do_GET(self):
        """处理GET请求"""
        # 解析URL
        parsed_url = urllib.parse.urlparse(self.path)
        pathname = parsed_url.path
        
        # 移除尾部斜杠（除了根路径）
        if pathname != '/' and pathname.endswith('/'):
            pathname = pathname.rstrip('/')
        
        print(f"GET {pathname}")
        
        # 构建文件路径
        file_path = os.path.join(ROOT_DIR, pathname.lstrip('/'))
        
        # 如果是目录，尝试查找index.html
        if os.path.isdir(file_path):
            file_path = os.path.join(file_path, 'index.html')
        
        # 检查文件是否存在
        if os.path.isfile(file_path):
            self.serve_static_file(file_path)
            return
        
        # 静态文件不存在，检查是否是客户端路由
        html_file = self.match_client_route(pathname + '/')  # 添加尾部斜杠进行匹配
        if html_file:
            html_path = os.path.join(ROOT_DIR, html_file)
            if os.path.isfile(html_path):
                print(f"  → Serving {html_file} for client route {pathname}")
                self.serve_static_file(html_path)
                return
        
        # 404 - 文件不存在且不是客户端路由
        self.send_404()
    
    def do_OPTIONS(self):
        """处理OPTIONS请求（CORS预检）"""
        self.send_response(200)
        self.end_headers()
    
    def serve_static_file(self, file_path):
        """提供静态文件服务"""
        try:
            with open(file_path, 'rb') as f:
                content = f.read()
            
            # 获取MIME类型
            mime_type, _ = mimetypes.guess_type(file_path)
            if mime_type is None:
                mime_type = 'text/plain'
            
            self.send_response(200)
            self.send_header('Content-Type', mime_type)
            self.send_header('Content-Length', str(len(content)))
            self.end_headers()
            self.wfile.write(content)
            
        except IOError:
            self.send_404()
    
    def match_client_route(self, pathname):
        """匹配客户端路由"""
        # 精确匹配
        if pathname in CLIENT_ROUTES:
            return CLIENT_ROUTES[pathname]
        
        # 模式匹配
        for pattern, html_file in CLIENT_ROUTES.items():
            if pathname.startswith(pattern) and pattern != '/':
                return html_file
        
        # 默认返回首页
        return CLIENT_ROUTES['/']
    
    def send_404(self):
        """发送404错误页面"""
        print(f"  → 404 Not Found")
        
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>404 - Page Not Found</title>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <style>
                body {{ 
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    text-align: center; 
                    padding: 50px;
                    background: #f8f9fa;
                    color: #333;
                }}
                .container {{
                    max-width: 600px;
                    margin: 0 auto;
                    background: white;
                    padding: 40px;
                    border-radius: 8px;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                }}
                .error {{ 
                    color: #e74c3c; 
                    font-size: 4em;
                    margin: 0;
                    font-weight: bold;
                }}
                .message {{
                    font-size: 1.2em;
                    margin: 20px 0;
                    color: #666;
                }}
                .path {{
                    background: #f1f2f6;
                    padding: 10px;
                    border-radius: 4px;
                    font-family: monospace;
                    margin: 20px 0;
                }}
                .back-link {{ 
                    color: #3498db; 
                    text-decoration: none;
                    font-weight: bold;
                    display: inline-block;
                    margin-top: 20px;
                    padding: 10px 20px;
                    background: #3498db;
                    color: white;
                    border-radius: 4px;
                    transition: background 0.3s;
                }}
                .back-link:hover {{
                    background: #2980b9;
                }}
            </style>
        </head>
        <body>
            <div class="container">
                <h1 class="error">404</h1>
                <p class="message">Page Not Found</p>
                <div class="path">The requested page <strong>{self.path}</strong> could not be found.</div>
                <a href="/" class="back-link">← Back to Home</a>
            </div>
        </body>
        </html>
        """.encode('utf-8')
        
        self.send_response(404)
        self.send_header('Content-Type', 'text/html; charset=utf-8')
        self.send_header('Content-Length', str(len(html_content)))
        self.end_headers()
        self.wfile.write(html_content)

def run_server():
    """启动开发服务器"""
    try:
        server = HTTPServer((HOST, PORT), DevServerHandler)
        
        print('🚀 Development Server Started')
        print(f'📍 Server running at: http://{HOST}:{PORT}/')
        print(f'📁 Serving files from: {ROOT_DIR}')
        print('🔄 Client-side routing enabled for:')
        for route, file in CLIENT_ROUTES.items():
            print(f'   {route}* → {file}')
        print('\n✨ Ready for development!')
        print('Press Ctrl+C to stop the server\n')
        
        server.serve_forever()
        
    except KeyboardInterrupt:
        print('\n🛑 Shutting down development server...')
        server.shutdown()
        print('✅ Server stopped')
    except OSError as e:
        if e.errno == 48:  # Address already in use
            print(f'❌ Port {PORT} is already in use')
            print('💡 Try using a different port: PORT=3002 python3 dev_server.py')
        else:
            print(f'❌ Server error: {e}')
        sys.exit(1)

if __name__ == '__main__':
    run_server()
