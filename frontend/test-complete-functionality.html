<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Complete Functionality Test - Quotese.com</title>
    <link href="https://cdn.staticfile.org/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <link href="https://cdn.staticfile.org/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="/css/styles.css" rel="stylesheet">
    <link href="/css/animations.css" rel="stylesheet">
</head>
<body class="bg-gray-100 dark:bg-gray-900">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-6xl mx-auto">
            <h1 class="text-4xl font-bold text-center mb-8 text-gray-800 dark:text-gray-200">
                <i class="fas fa-check-double text-green-500 mr-3"></i>
                Complete Functionality Test
            </h1>

            <!-- Test Summary -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-8">
                <h2 class="text-2xl font-semibold mb-4 text-gray-800 dark:text-gray-200">Test Summary</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div class="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                        <i class="fas fa-code text-green-600 dark:text-green-400 text-2xl mb-2"></i>
                        <h3 class="font-semibold text-green-800 dark:text-green-200">API Method</h3>
                        <p class="text-sm text-green-600 dark:text-green-400">getQuote() Added</p>
                    </div>
                    <div class="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                        <i class="fas fa-mouse-pointer text-blue-600 dark:text-blue-400 text-2xl mb-2"></i>
                        <h3 class="font-semibold text-blue-800 dark:text-blue-200">Click Function</h3>
                        <p class="text-sm text-blue-600 dark:text-blue-400">Cards Clickable</p>
                    </div>
                    <div class="text-center p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                        <i class="fas fa-exclamation-triangle text-yellow-600 dark:text-yellow-400 text-2xl mb-2"></i>
                        <h3 class="font-semibold text-yellow-800 dark:text-yellow-200">Error Handling</h3>
                        <p class="text-sm text-yellow-600 dark:text-yellow-400">404 & Retry</p>
                    </div>
                    <div class="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                        <i class="fas fa-tachometer-alt text-purple-600 dark:text-purple-400 text-2xl mb-2"></i>
                        <h3 class="font-semibold text-purple-800 dark:text-purple-200">Performance</h3>
                        <p class="text-sm text-purple-600 dark:text-purple-400">Optimized</p>
                    </div>
                </div>
            </div>

            <!-- Test Controls -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                <!-- API Tests -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                    <h3 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">
                        <i class="fas fa-flask text-green-500 mr-2"></i>
                        API Tests
                    </h3>
                    <div class="space-y-3">
                        <button id="testGetQuote" class="w-full px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors duration-300">
                            <i class="fas fa-play mr-2"></i>Test getQuote() Method
                        </button>
                        <button id="testCacheStats" class="w-full px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors duration-300">
                            <i class="fas fa-chart-bar mr-2"></i>Show Cache Statistics
                        </button>
                        <button id="testPreload" class="w-full px-4 py-2 bg-purple-500 text-white rounded-md hover:bg-purple-600 transition-colors duration-300">
                            <i class="fas fa-download mr-2"></i>Test Preloading
                        </button>
                    </div>
                </div>

                <!-- Navigation Tests -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                    <h3 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">
                        <i class="fas fa-link text-blue-500 mr-2"></i>
                        Navigation Tests
                    </h3>
                    <div class="space-y-3">
                        <button id="testQuoteDetail" class="w-full px-4 py-2 bg-yellow-500 text-white rounded-md hover:bg-yellow-600 transition-colors duration-300">
                            <i class="fas fa-external-link-alt mr-2"></i>Test Quote Detail Page
                        </button>
                        <button id="test404" class="w-full px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors duration-300">
                            <i class="fas fa-times-circle mr-2"></i>Test 404 Handling
                        </button>
                        <button id="testRetry" class="w-full px-4 py-2 bg-orange-500 text-white rounded-md hover:bg-orange-600 transition-colors duration-300">
                            <i class="fas fa-redo mr-2"></i>Test Retry Mechanism
                        </button>
                    </div>
                </div>
            </div>

            <!-- Test Results -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
                <h3 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">
                    <i class="fas fa-clipboard-list text-gray-500 mr-2"></i>
                    Test Results
                </h3>
                <div id="testResults" class="space-y-3 max-h-96 overflow-y-auto">
                    <p class="text-gray-500 dark:text-gray-400 italic">Click test buttons to see results...</p>
                </div>
            </div>

            <!-- Sample Quote Cards -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                <h3 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">
                    <i class="fas fa-quote-right text-yellow-500 mr-2"></i>
                    Sample Quote Cards (Click to Test)
                </h3>
                <div id="sampleQuotes" class="space-y-4">
                    <p class="text-gray-500 dark:text-gray-400 italic text-center py-8">Loading sample quotes...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script>
        // App Configuration
        window.AppConfig = {
            apiEndpoint: 'http://localhost:8000/api/',
            graphqlEndpoint: 'http://localhost:8000/graphql/',
            debug: true
        };
    </script>
    
    <!-- Load required scripts -->
    <script src="/js/debug.js?v=20250626"></script>
    <script src="/js/mock-data.js?v=20250626"></script>
    <script src="/js/api-client.js?v=20250626"></script>
    <script src="/js/url-handler.js?v=20250626"></script>
    <script src="/js/components/quote-card.js?v=20250626"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const testResults = document.getElementById('testResults');
            const sampleQuotes = document.getElementById('sampleQuotes');

            // Test functions
            const tests = {
                async testGetQuote() {
                    addResult('🧪 Testing getQuote() method...', 'info');
                    
                    try {
                        const quote = await window.ApiClient.getQuote(1);
                        if (quote) {
                            addResult(`✅ getQuote() works! Quote: "${quote.content.substring(0, 50)}..."`, 'success');
                            return true;
                        } else {
                            addResult('❌ getQuote() returned null', 'error');
                            return false;
                        }
                    } catch (error) {
                        addResult(`❌ getQuote() failed: ${error.message}`, 'error');
                        return false;
                    }
                },

                async testCacheStats() {
                    addResult('📊 Checking cache statistics...', 'info');
                    
                    if (window.ApiClient.getCacheStats) {
                        const stats = window.ApiClient.getCacheStats();
                        addResult(`📈 Cache Stats: ${stats.entryCount} entries, ${Math.round(stats.totalSize/1024)}KB total, ${stats.hitRate}% hit rate`, 'success');
                    } else {
                        addResult('❌ Cache statistics not available', 'error');
                    }
                },

                async testPreload() {
                    addResult('⬇️ Testing preload functionality...', 'info');
                    
                    try {
                        // Test multiple quotes to trigger preloading
                        const promises = [1, 2, 3, 4, 5].map(id => window.ApiClient.getQuote(id));
                        await Promise.all(promises);
                        addResult('✅ Preload test completed - check console for preload logs', 'success');
                    } catch (error) {
                        addResult(`❌ Preload test failed: ${error.message}`, 'error');
                    }
                },

                testQuoteDetail() {
                    addResult('🔗 Testing quote detail navigation...', 'info');
                    const url = '/quotes/1/';
                    window.open(url, '_blank');
                    addResult(`✅ Opened quote detail page: ${url}`, 'success');
                },

                test404() {
                    addResult('🚫 Testing 404 handling...', 'info');
                    const url = '/quotes/999999/';
                    window.open(url, '_blank');
                    addResult(`✅ Opened non-existent quote page: ${url}`, 'success');
                },

                testRetry() {
                    addResult('🔄 Testing retry mechanism...', 'info');
                    addResult('ℹ️ Retry mechanism is tested automatically when API calls fail', 'info');
                    addResult('✅ Check browser console for retry logs during network issues', 'success');
                }
            };

            // Add result function
            function addResult(message, type = 'info') {
                const div = document.createElement('div');
                const colors = {
                    success: 'bg-green-100 border-green-400 text-green-700 dark:bg-green-900/20 dark:border-green-800 dark:text-green-200',
                    error: 'bg-red-100 border-red-400 text-red-700 dark:bg-red-900/20 dark:border-red-800 dark:text-red-200',
                    warning: 'bg-yellow-100 border-yellow-400 text-yellow-700 dark:bg-yellow-900/20 dark:border-yellow-800 dark:text-yellow-200',
                    info: 'bg-blue-100 border-blue-400 text-blue-700 dark:bg-blue-900/20 dark:border-blue-800 dark:text-blue-200'
                };
                
                div.className = `p-3 border rounded-md ${colors[type] || colors.info}`;
                div.innerHTML = `
                    <div class="flex items-center text-sm">
                        <span class="font-mono text-xs mr-3">${new Date().toLocaleTimeString()}</span>
                        <span>${message}</span>
                    </div>
                `;
                
                testResults.appendChild(div);
                div.scrollIntoView({ behavior: 'smooth' });
            }

            // Load sample quotes
            async function loadSampleQuotes() {
                try {
                    const quotesData = await window.ApiClient.getQuotes(1, 3);
                    if (quotesData && quotesData.quotes) {
                        window.QuoteCardComponent.renderList(quotesData.quotes, 'sampleQuotes', {
                            showAuthorAvatar: true,
                            showActions: true,
                            emptyMessage: 'No quotes found.'
                        });
                        addResult(`✅ Loaded ${quotesData.quotes.length} sample quote cards`, 'success');
                    }
                } catch (error) {
                    sampleQuotes.innerHTML = `<p class="text-red-500 text-center py-8">Error loading quotes: ${error.message}</p>`;
                    addResult(`❌ Failed to load sample quotes: ${error.message}`, 'error');
                }
            }

            // Event listeners
            Object.keys(tests).forEach(testName => {
                const button = document.getElementById(testName);
                if (button) {
                    button.addEventListener('click', tests[testName]);
                }
            });

            // Initialize
            setTimeout(() => {
                addResult('🚀 Complete functionality test page loaded', 'success');
                addResult(`📋 Available tests: ${Object.keys(tests).length}`, 'info');
                loadSampleQuotes();
            }, 1000);

            console.log('Complete Functionality Test page loaded');
        });
    </script>
</body>
</html>
