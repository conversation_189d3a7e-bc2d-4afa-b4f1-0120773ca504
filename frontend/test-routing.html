<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Routing Test - Quotese.com</title>
    <link href="https://cdn.staticfile.org/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <link href="https://cdn.staticfile.org/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-gray-100 dark:bg-gray-900">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            <h1 class="text-4xl font-bold text-center mb-8 text-gray-800 dark:text-gray-200">
                <i class="fas fa-route text-blue-500 mr-3"></i>
                Client-Side Routing Test
            </h1>

            <!-- Status Banner -->
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg mb-8">
                <div class="flex items-center">
                    <i class="fas fa-check-circle mr-3 text-xl"></i>
                    <div>
                        <h3 class="font-semibold">✅ Routing Problem Solved!</h3>
                        <p class="text-sm">All client-side routes now work correctly with the new development server.</p>
                    </div>
                </div>
            </div>

            <!-- Test Routes -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                <!-- Quote Detail Routes -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                    <h3 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">
                        <i class="fas fa-quote-right text-yellow-500 mr-2"></i>
                        Quote Detail Pages
                    </h3>
                    <div class="space-y-3">
                        <a href="/quotes/1/" target="_blank" class="block p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg hover:bg-yellow-100 dark:hover:bg-yellow-900/30 transition-colors duration-300">
                            <div class="flex items-center justify-between">
                                <span class="font-mono text-sm">/quotes/1/</span>
                                <i class="fas fa-external-link-alt text-yellow-600 dark:text-yellow-400"></i>
                            </div>
                            <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">Einstein Quote</p>
                        </a>
                        
                        <a href="/quotes/2/" target="_blank" class="block p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg hover:bg-yellow-100 dark:hover:bg-yellow-900/30 transition-colors duration-300">
                            <div class="flex items-center justify-between">
                                <span class="font-mono text-sm">/quotes/2/</span>
                                <i class="fas fa-external-link-alt text-yellow-600 dark:text-yellow-400"></i>
                            </div>
                            <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">Steve Jobs Quote</p>
                        </a>
                        
                        <a href="/quotes/999999/" target="_blank" class="block p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg hover:bg-red-100 dark:hover:bg-red-900/30 transition-colors duration-300">
                            <div class="flex items-center justify-between">
                                <span class="font-mono text-sm">/quotes/999999/</span>
                                <i class="fas fa-external-link-alt text-red-600 dark:text-red-400"></i>
                            </div>
                            <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">Test 404 Handling</p>
                        </a>
                    </div>
                </div>

                <!-- Category Routes -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                    <h3 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">
                        <i class="fas fa-tags text-blue-500 mr-2"></i>
                        Category Pages
                    </h3>
                    <div class="space-y-3">
                        <a href="/categories/work/" target="_blank" class="block p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors duration-300">
                            <div class="flex items-center justify-between">
                                <span class="font-mono text-sm">/categories/work/</span>
                                <i class="fas fa-external-link-alt text-blue-600 dark:text-blue-400"></i>
                            </div>
                            <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">Work Category</p>
                        </a>
                        
                        <a href="/categories/success/" target="_blank" class="block p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors duration-300">
                            <div class="flex items-center justify-between">
                                <span class="font-mono text-sm">/categories/success/</span>
                                <i class="fas fa-external-link-alt text-blue-600 dark:text-blue-400"></i>
                            </div>
                            <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">Success Category</p>
                        </a>
                        
                        <a href="/categories/life/" target="_blank" class="block p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors duration-300">
                            <div class="flex items-center justify-between">
                                <span class="font-mono text-sm">/categories/life/</span>
                                <i class="fas fa-external-link-alt text-blue-600 dark:text-blue-400"></i>
                            </div>
                            <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">Life Category</p>
                        </a>
                    </div>
                </div>

                <!-- Author Routes -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                    <h3 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">
                        <i class="fas fa-user text-green-500 mr-2"></i>
                        Author Pages
                    </h3>
                    <div class="space-y-3">
                        <a href="/authors/albert-einstein/" target="_blank" class="block p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors duration-300">
                            <div class="flex items-center justify-between">
                                <span class="font-mono text-sm">/authors/albert-einstein/</span>
                                <i class="fas fa-external-link-alt text-green-600 dark:text-green-400"></i>
                            </div>
                            <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">Albert Einstein</p>
                        </a>
                        
                        <a href="/authors/steve-jobs/" target="_blank" class="block p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors duration-300">
                            <div class="flex items-center justify-between">
                                <span class="font-mono text-sm">/authors/steve-jobs/</span>
                                <i class="fas fa-external-link-alt text-green-600 dark:text-green-400"></i>
                            </div>
                            <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">Steve Jobs</p>
                        </a>
                        
                        <a href="/authors/ray-bradbury/" target="_blank" class="block p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors duration-300">
                            <div class="flex items-center justify-between">
                                <span class="font-mono text-sm">/authors/ray-bradbury/</span>
                                <i class="fas fa-external-link-alt text-green-600 dark:text-green-400"></i>
                            </div>
                            <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">Ray Bradbury</p>
                        </a>
                    </div>
                </div>

                <!-- Source Routes -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                    <h3 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">
                        <i class="fas fa-book text-purple-500 mr-2"></i>
                        Source Pages
                    </h3>
                    <div class="space-y-3">
                        <a href="/sources/the-alchemist/" target="_blank" class="block p-3 bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg hover:bg-purple-100 dark:hover:bg-purple-900/30 transition-colors duration-300">
                            <div class="flex items-center justify-between">
                                <span class="font-mono text-sm">/sources/the-alchemist/</span>
                                <i class="fas fa-external-link-alt text-purple-600 dark:text-purple-400"></i>
                            </div>
                            <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">The Alchemist</p>
                        </a>
                        
                        <a href="/sources/think-and-grow-rich/" target="_blank" class="block p-3 bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg hover:bg-purple-100 dark:hover:bg-purple-900/30 transition-colors duration-300">
                            <div class="flex items-center justify-between">
                                <span class="font-mono text-sm">/sources/think-and-grow-rich/</span>
                                <i class="fas fa-external-link-alt text-purple-600 dark:text-purple-400"></i>
                            </div>
                            <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">Think and Grow Rich</p>
                        </a>
                        
                        <a href="/sources/speech/" target="_blank" class="block p-3 bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg hover:bg-purple-100 dark:hover:bg-purple-900/30 transition-colors duration-300">
                            <div class="flex items-center justify-between">
                                <span class="font-mono text-sm">/sources/speech/</span>
                                <i class="fas fa-external-link-alt text-purple-600 dark:text-purple-400"></i>
                            </div>
                            <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">Speech</p>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Test Results -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
                <h3 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">
                    <i class="fas fa-clipboard-check text-gray-500 mr-2"></i>
                    Test Results
                </h3>
                <div id="testResults" class="space-y-3">
                    <p class="text-gray-500 dark:text-gray-400 italic">Click the links above to test routing...</p>
                </div>
            </div>

            <!-- Instructions -->
            <div class="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded-lg">
                <div class="flex items-start">
                    <i class="fas fa-info-circle mr-3 mt-1"></i>
                    <div>
                        <h3 class="font-semibold mb-2">How to Test</h3>
                        <ol class="list-decimal list-inside space-y-1 text-sm">
                            <li>Click any link above to test that route</li>
                            <li>Verify the page loads correctly (not 404)</li>
                            <li>Check that the URL in the address bar matches the link</li>
                            <li>Try refreshing the page - it should still work</li>
                            <li>Test the 404 link to verify error handling</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Track link clicks for testing
        document.addEventListener('DOMContentLoaded', function() {
            const testResults = document.getElementById('testResults');
            let testCount = 0;

            // Add click tracking to all test links
            document.querySelectorAll('a[href^="/"]').forEach(link => {
                link.addEventListener('click', function(e) {
                    testCount++;
                    const url = this.getAttribute('href');
                    
                    const div = document.createElement('div');
                    div.className = 'p-3 bg-blue-100 border border-blue-400 text-blue-700 rounded-md';
                    div.innerHTML = `
                        <div class="flex items-center text-sm">
                            <span class="font-mono text-xs mr-3">${new Date().toLocaleTimeString()}</span>
                            <span>Test #${testCount}: Opening <code>${url}</code></span>
                        </div>
                    `;
                    
                    testResults.appendChild(div);
                    div.scrollIntoView({ behavior: 'smooth' });
                });
            });

            console.log('Routing test page loaded');
            console.log('Development server should be running with client-side routing support');
        });
    </script>
</body>
</html>
