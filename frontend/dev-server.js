#!/usr/bin/env node

/**
 * 开发服务器 - 支持客户端路由的静态文件服务器
 * 解决SPA应用的路由重写问题
 */

const http = require('http');
const fs = require('fs');
const path = require('path');
const url = require('url');

// 服务器配置
const PORT = process.env.PORT || 3001;
const HOST = process.env.HOST || 'localhost';
const ROOT_DIR = __dirname; // frontend目录

// MIME类型映射
const MIME_TYPES = {
    '.html': 'text/html',
    '.js': 'text/javascript',
    '.css': 'text/css',
    '.json': 'application/json',
    '.png': 'image/png',
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.gif': 'image/gif',
    '.svg': 'image/svg+xml',
    '.ico': 'image/x-icon',
    '.woff': 'font/woff',
    '.woff2': 'font/woff2',
    '.ttf': 'font/ttf',
    '.eot': 'application/vnd.ms-fontobject'
};

// 客户端路由规则 - 这些路径应该返回相应的HTML页面
const CLIENT_ROUTES = {
    // 名言详情页路由
    '/quotes/': 'quote.html',
    // 分类页面路由
    '/categories/': 'category.html',
    // 作者页面路由
    '/authors/': 'author.html',
    // 来源页面路由
    '/sources/': 'source.html',
    // 默认首页
    '/': 'index.html'
};

/**
 * 获取文件的MIME类型
 */
function getMimeType(filePath) {
    const ext = path.extname(filePath).toLowerCase();
    return MIME_TYPES[ext] || 'text/plain';
}

/**
 * 检查文件是否存在
 */
function fileExists(filePath) {
    try {
        return fs.statSync(filePath).isFile();
    } catch (err) {
        return false;
    }
}

/**
 * 读取文件内容
 */
function readFile(filePath) {
    try {
        return fs.readFileSync(filePath);
    } catch (err) {
        return null;
    }
}

/**
 * 匹配客户端路由
 */
function matchClientRoute(pathname) {
    // 精确匹配
    if (CLIENT_ROUTES[pathname]) {
        return CLIENT_ROUTES[pathname];
    }
    
    // 模式匹配
    for (const [pattern, htmlFile] of Object.entries(CLIENT_ROUTES)) {
        if (pathname.startsWith(pattern) && pattern !== '/') {
            return htmlFile;
        }
    }
    
    // 默认返回首页
    return CLIENT_ROUTES['/'];
}

/**
 * 处理HTTP请求
 */
function handleRequest(req, res) {
    const parsedUrl = url.parse(req.url, true);
    let pathname = parsedUrl.pathname;
    
    // 移除尾部斜杠（除了根路径）
    if (pathname !== '/' && pathname.endsWith('/')) {
        pathname = pathname.slice(0, -1);
    }
    
    console.log(`${new Date().toISOString()} - ${req.method} ${pathname}`);
    
    // 构建文件路径
    let filePath = path.join(ROOT_DIR, pathname);
    
    // 如果是目录，尝试查找index.html
    if (fs.existsSync(filePath) && fs.statSync(filePath).isDirectory()) {
        filePath = path.join(filePath, 'index.html');
    }
    
    // 检查文件是否存在
    if (fileExists(filePath)) {
        // 静态文件存在，直接返回
        const content = readFile(filePath);
        if (content) {
            const mimeType = getMimeType(filePath);
            res.writeHead(200, {
                'Content-Type': mimeType,
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            });
            res.end(content);
            return;
        }
    }
    
    // 静态文件不存在，检查是否是客户端路由
    const htmlFile = matchClientRoute(pathname + '/'); // 添加尾部斜杠进行匹配
    if (htmlFile) {
        const htmlPath = path.join(ROOT_DIR, htmlFile);
        const content = readFile(htmlPath);
        
        if (content) {
            console.log(`  → Serving ${htmlFile} for client route ${pathname}`);
            res.writeHead(200, {
                'Content-Type': 'text/html',
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            });
            res.end(content);
            return;
        }
    }
    
    // 404 - 文件不存在且不是客户端路由
    console.log(`  → 404 Not Found`);
    res.writeHead(404, { 'Content-Type': 'text/html' });
    res.end(`
        <!DOCTYPE html>
        <html>
        <head>
            <title>404 - Page Not Found</title>
            <style>
                body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
                .error { color: #e74c3c; }
                .back-link { color: #3498db; text-decoration: none; }
            </style>
        </head>
        <body>
            <h1 class="error">404 - Page Not Found</h1>
            <p>The requested page <code>${pathname}</code> could not be found.</p>
            <p><a href="/" class="back-link">← Back to Home</a></p>
        </body>
        </html>
    `);
}

/**
 * 创建并启动服务器
 */
const server = http.createServer(handleRequest);

server.listen(PORT, HOST, () => {
    console.log('🚀 Development Server Started');
    console.log(`📍 Server running at: http://${HOST}:${PORT}/`);
    console.log(`📁 Serving files from: ${ROOT_DIR}`);
    console.log('🔄 Client-side routing enabled for:');
    Object.entries(CLIENT_ROUTES).forEach(([route, file]) => {
        console.log(`   ${route}* → ${file}`);
    });
    console.log('\n✨ Ready for development!');
    console.log('Press Ctrl+C to stop the server\n');
});

// 优雅关闭
process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down development server...');
    server.close(() => {
        console.log('✅ Server stopped');
        process.exit(0);
    });
});

// 错误处理
server.on('error', (err) => {
    if (err.code === 'EADDRINUSE') {
        console.error(`❌ Port ${PORT} is already in use`);
        console.log('💡 Try using a different port: PORT=3002 node dev-server.js');
    } else {
        console.error('❌ Server error:', err);
    }
    process.exit(1);
});
