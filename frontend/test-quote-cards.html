<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quote Cards Click Test - Quotese.com</title>
    <link href="https://cdn.staticfile.org/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <link href="https://cdn.staticfile.org/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="/css/styles.css" rel="stylesheet">
    <link href="/css/animations.css" rel="stylesheet">
</head>
<body class="bg-gray-100 dark:bg-gray-900">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            <h1 class="text-3xl font-bold text-center mb-8 text-gray-800 dark:text-gray-200">
                <i class="fas fa-mouse-pointer text-yellow-500 mr-2"></i>
                Quote Cards Click Test
            </h1>

            <!-- Test Controls -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
                <h2 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">Test Controls</h2>
                
                <div class="flex flex-wrap gap-4 items-end">
                    <button id="loadQuotesBtn" 
                            class="px-6 py-2 bg-yellow-500 text-white rounded-md hover:bg-yellow-600 transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-yellow-500">
                        <i class="fas fa-download mr-2"></i>
                        Load Test Quotes
                    </button>
                    
                    <button id="clearBtn" 
                            class="px-6 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600 transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-gray-500">
                        <i class="fas fa-trash mr-2"></i>
                        Clear
                    </button>
                </div>
            </div>

            <!-- Test Results -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
                <h2 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">Click Test Results</h2>
                
                <div id="clickResults" class="space-y-2">
                    <p class="text-gray-500 dark:text-gray-400 italic">Click on quote cards below to test navigation</p>
                </div>
            </div>

            <!-- Quote Cards Container -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                <h2 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200">Test Quote Cards</h2>
                
                <div id="loadingIndicator" class="hidden text-center py-8">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-500"></div>
                    <p class="mt-2 text-gray-600 dark:text-gray-400">Loading quotes...</p>
                </div>
                
                <div id="quotesContainer" class="space-y-4">
                    <p class="text-gray-500 dark:text-gray-400 italic text-center py-8">Click "Load Test Quotes" to display quote cards</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script>
        // App Configuration
        window.AppConfig = {
            apiEndpoint: 'http://localhost:8000/api/',
            graphqlEndpoint: 'http://localhost:8000/graphql/',
            debug: true
        };
    </script>
    
    <!-- Load required scripts -->
    <script src="/js/debug.js?v=20250626"></script>
    <script src="/js/mock-data.js?v=20250626"></script>
    <script src="/js/api-client.js?v=20250626"></script>
    <script src="/js/url-handler.js?v=20250626"></script>
    <script src="/js/components/quote-card.js?v=20250626"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const loadQuotesBtn = document.getElementById('loadQuotesBtn');
            const clearBtn = document.getElementById('clearBtn');
            const loadingIndicator = document.getElementById('loadingIndicator');
            const quotesContainer = document.getElementById('quotesContainer');
            const clickResults = document.getElementById('clickResults');

            let clickCount = 0;

            // Load test quotes
            async function loadTestQuotes() {
                loadingIndicator.classList.remove('hidden');
                quotesContainer.innerHTML = '';

                try {
                    // Test if ApiClient is available
                    if (!window.ApiClient) {
                        throw new Error('ApiClient not available');
                    }

                    // Load some test quotes
                    console.log('Loading test quotes...');
                    const quotesData = await window.ApiClient.getQuotes(1, 5);

                    if (quotesData && quotesData.quotes && quotesData.quotes.length > 0) {
                        // Use QuoteCardComponent to render quotes
                        window.QuoteCardComponent.renderList(quotesData.quotes, 'quotesContainer', {
                            showAuthorAvatar: true,
                            showActions: true,
                            highlightCurrentCategory: false,
                            emptyMessage: 'No quotes found.'
                        });

                        // Add click tracking to all quote cards
                        addClickTracking();

                        addClickResult(`✅ Successfully loaded ${quotesData.quotes.length} quote cards`, 'success');
                    } else {
                        throw new Error('No quotes returned from API');
                    }

                } catch (error) {
                    console.error('Error loading quotes:', error);
                    quotesContainer.innerHTML = `
                        <div class="text-center py-8">
                            <i class="fas fa-exclamation-triangle text-red-500 text-4xl mb-4"></i>
                            <p class="text-red-600 dark:text-red-400">Error loading quotes: ${error.message}</p>
                        </div>
                    `;
                    addClickResult(`❌ Error loading quotes: ${error.message}`, 'error');
                } finally {
                    loadingIndicator.classList.add('hidden');
                }
            }

            // Add click tracking to quote cards
            function addClickTracking() {
                const quoteCards = document.querySelectorAll('[data-quote-id]');
                
                quoteCards.forEach(card => {
                    // Remove existing listeners to avoid duplicates
                    card.removeEventListener('click', handleQuoteCardClick);
                    
                    // Add new listener
                    card.addEventListener('click', handleQuoteCardClick);
                });

                console.log(`Added click tracking to ${quoteCards.length} quote cards`);
            }

            // Handle quote card click
            function handleQuoteCardClick(e) {
                // Prevent default navigation for testing
                e.preventDefault();
                e.stopPropagation();

                const quoteId = this.getAttribute('data-quote-id');
                clickCount++;

                if (quoteId) {
                    const quoteUrl = window.UrlHandler.getQuoteUrl({ id: quoteId });
                    addClickResult(`🖱️ Click #${clickCount}: Quote ID ${quoteId} → ${quoteUrl}`, 'info');
                    
                    // Show confirmation dialog
                    if (confirm(`Navigate to quote detail page?\n\nQuote ID: ${quoteId}\nURL: ${quoteUrl}`)) {
                        window.open(quoteUrl, '_blank');
                    }
                } else {
                    addClickResult(`⚠️ Click #${clickCount}: No quote ID found`, 'warning');
                }
            }

            // Add click result
            function addClickResult(message, type = 'info') {
                const div = document.createElement('div');
                const colors = {
                    success: 'bg-green-100 border-green-400 text-green-700',
                    error: 'bg-red-100 border-red-400 text-red-700',
                    warning: 'bg-yellow-100 border-yellow-400 text-yellow-700',
                    info: 'bg-blue-100 border-blue-400 text-blue-700'
                };
                
                div.className = `p-3 border rounded-md ${colors[type] || colors.info}`;
                div.innerHTML = `
                    <div class="flex items-center text-sm">
                        <span class="font-mono">${new Date().toLocaleTimeString()}</span>
                        <span class="ml-3">${message}</span>
                    </div>
                `;
                
                clickResults.appendChild(div);
                
                // Scroll to bottom
                div.scrollIntoView({ behavior: 'smooth' });
            }

            // Clear results
            function clearResults() {
                clickResults.innerHTML = '<p class="text-gray-500 dark:text-gray-400 italic">Click on quote cards below to test navigation</p>';
                quotesContainer.innerHTML = '<p class="text-gray-500 dark:text-gray-400 italic text-center py-8">Click "Load Test Quotes" to display quote cards</p>';
                clickCount = 0;
            }

            // Event listeners
            loadQuotesBtn.addEventListener('click', loadTestQuotes);
            clearBtn.addEventListener('click', clearResults);

            console.log('Quote Cards Click Test page loaded');
            console.log('ApiClient available:', !!window.ApiClient);
            console.log('QuoteCardComponent available:', !!window.QuoteCardComponent);
            console.log('UrlHandler available:', !!window.UrlHandler);
        });
    </script>
</body>
</html>
